using Castle.DynamicProxy;
using Core.Utilities.Interceptors;
using System;
using System.Threading;

namespace Core.Aspects.Autofac.Retry
{
    /// <summary>
    /// Retry Aspect - Concurrency ve geçici hatalar için retry mekanizması
    /// </summary>
    public class RetryAspect : MethodInterception
    {
        private readonly int _maxRetryCount;
        private readonly int _delayMilliseconds;
        private readonly Type[] _retryableExceptions;

        public RetryAspect(
            int maxRetryCount = 3, 
            int delayMilliseconds = 100,
            params Type[] retryableExceptions)
        {
            _maxRetryCount = maxRetryCount;
            _delayMilliseconds = delayMilliseconds;
            _retryableExceptions = retryableExceptions ?? new Type[] 
            { 
                typeof(InvalidOperationException),
                typeof(TimeoutException),
                typeof(System.Data.SqlClient.SqlException)
            };
        }

        public override void Intercept(IInvocation invocation)
        {
            var attempt = 0;
            Exception lastException = null;

            while (attempt <= _maxRetryCount)
            {
                try
                {
                    invocation.Proceed();
                    return; // Başarılı, retry'a gerek yok
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    attempt++;

                    // Son deneme ise veya retry edilebilir hata değilse, exception'ı fırlat
                    if (attempt > _maxRetryCount || !IsRetryableException(ex))
                    {
                        throw;
                    }

                    // Retry öncesi bekleme
                    if (_delayMilliseconds > 0)
                    {
                        Thread.Sleep(_delayMilliseconds * attempt); // Exponential backoff
                    }

                    // Log retry attempt (opsiyonel)
                    System.Diagnostics.Debug.WriteLine(
                        $"Retry attempt {attempt}/{_maxRetryCount} for method {invocation.Method.Name}. " +
                        $"Exception: {ex.Message}");
                }
            }

            // Bu noktaya hiç gelmemeli, ama güvenlik için
            throw lastException ?? new Exception("Retry failed with unknown error");
        }

        private bool IsRetryableException(Exception ex)
        {
            foreach (var retryableType in _retryableExceptions)
            {
                if (retryableType.IsAssignableFrom(ex.GetType()))
                {
                    return true;
                }
            }

            // SQL Server concurrency hatalarını kontrol et
            if (ex is System.Data.SqlClient.SqlException sqlEx)
            {
                // Deadlock, timeout, constraint violation gibi hatalar
                return sqlEx.Number == 1205 || // Deadlock
                       sqlEx.Number == -2 ||    // Timeout
                       sqlEx.Number == 2627 ||  // Unique constraint violation
                       sqlEx.Number == 547;     // Foreign key constraint violation
            }

            // Entity Framework hatalarını kontrol et
            if (ex.Message.Contains("An error occurred while saving the entity changes") ||
                ex.Message.Contains("The instance of entity type") ||
                ex.Message.Contains("concurrency"))
            {
                return true;
            }

            return false;
        }
    }
}
